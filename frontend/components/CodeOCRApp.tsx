"use client";

import APISettingsDialog from "@/components/APISettingsDialog";
import ImageUpload from "@/components/ImageUpload";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { BundledLanguage } from "@/components/ui/kibo-ui/code-block";
import {
  CodeBlock,
  CodeBlockBody,
  CodeBlockContent,
  CodeBlockCopyButton,
  CodeBlockFilename,
  CodeBlockFiles,
  CodeBlockHeader,
  CodeBlockItem,
} from "@/components/ui/kibo-ui/code-block";
import { Loader2, ScanText, Code } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface CodeOCRResponse {
  code: string;
}

interface APISettings {
  apiKey: string;
  apiBase: string;
  modelName: string;
}

export default function CodeOCRApp() {
  const t = useTranslations("CodeOCR");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<CodeOCRResponse | null>(null);
  const [apiSettings, setApiSettings] = useState<APISettings>({
    apiKey: "",
    apiBase: "",
    modelName: "",
  });

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setResult(null);
  }, []);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setResult(null);
  }, []);

  const processImage = useCallback(async () => {
    if (!selectedFile) {
      toast.error(t("selectImageFirst"));
      return;
    }
    if (!apiSettings.apiKey) {
      toast.error(t("apiKeyRequired"));
      return;
    }
    if (!apiSettings.modelName) {
      toast.error(t("modelNameRequired"));
      return;
    }

    setIsProcessing(true);
    setResult(null);

    const reader = new FileReader();
    reader.readAsDataURL(selectedFile);
    reader.onload = async () => {
      const base64Image = reader.result as string;
      const body = JSON.stringify({
        image_source: base64Image,
        api_key: apiSettings.apiKey,
        api_base: apiSettings.apiBase || "https://api.openai.com/v1",
        model_name: apiSettings.modelName,
      });

      try {
        const response = await fetch("http://127.0.0.1:8000/api/v1/codeocr", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body,
        });

        if (!response.ok) {
          // Try to parse error from backend, otherwise use a generic message
          const errorData = await response.json().catch(() => null);
          throw new Error(errorData?.detail || t("extractFailed"));
        }

        const data: CodeOCRResponse = await response.json();

        setResult(data);
        toast.success(t("extractSuccess"));
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : t("extractFailed");
        toast.error(errorMessage);
        // Optionally set result to show the error in the code block area
        setResult({
          code: `\`\`\`text\n--- ERROR ---\n${errorMessage}\n\`\`\``,
        });
      } finally {
        setIsProcessing(false);
      }
    };
    reader.onerror = (error) => {
      toast.error(t("imageReadFailed"));
      setIsProcessing(false);
    };
  }, [selectedFile, apiSettings, t]);

  // Helper function to parse language and code from the markdown block
  const parseCodeBlock = (rawCode: string | undefined) => {
    if (!rawCode) {
      return { language: "text", code: "" };
    }
    const match = rawCode.match(/^```(\w+)\n([\s\S]*)\n```$/);
    if (match) {
      return { language: match[1], code: match[2] };
    }
    // Fallback for raw text or malformed blocks
    return { language: "text", code: rawCode };
  };

  const { language, code } = parseCodeBlock(result?.code);

  return (
    <div className="w-full space-y-4">
      <Card className="shadow-md">
        <CardContent className="p-4 sm:p-6">
          <div className="space-y-4 sm:space-y-6">
            <ImageUpload
              onFileSelect={handleFileSelect}
              onRemoveFile={handleRemoveFile}
              selectedFile={selectedFile}
              disabled={isProcessing}
            />

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="w-full sm:w-auto">
                <APISettingsDialog onSettingsChange={setApiSettings} />
              </div>

              <Button
                onClick={processImage}
                disabled={!selectedFile || isProcessing}
                className="w-full sm:flex-1"
                size="default"
                variant="default"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("processing")}
                  </>
                ) : (
                  <>
                    <ScanText className="mr-2 h-4 w-4" />
                    {t("extractCode")}
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {result && code && (
        <CodeBlock
          data={[
            {
              language: language,
              filename: "",
              code: code,
            },
          ]}
          defaultValue={language}
        >
          <CodeBlockHeader>
            <CodeBlockFiles>
              {(item) => (
                <CodeBlockFilename key={item.language} value={item.language}>
                  <div className="flex items-center gap-2">
                    <Code /> {t("extractedCode")}
                  </div>
                </CodeBlockFilename>
              )}
            </CodeBlockFiles>
            <CodeBlockCopyButton value={code} />
          </CodeBlockHeader>
          <CodeBlockBody>
            {(item) => (
              <CodeBlockItem key={item.language} value={item.language}>
                <CodeBlockContent language={item.language as BundledLanguage}>
                  {item.code}
                </CodeBlockContent>
              </CodeBlockItem>
            )}
          </CodeBlockBody>
        </CodeBlock>
      )}

      {/* No code message */}
      {result && !code && (
        <div className="border rounded-lg p-8 text-center text-gray-500 dark:text-gray-400">
          {t("noCodeDetected")}
        </div>
      )}
    </div>
  );
}
