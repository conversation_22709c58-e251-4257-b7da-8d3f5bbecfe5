{"name": "codeocr", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "backend": "python backend/main.py", "dev:full": "concurrently \"pnpm dev\" \"pnpm backend\""}, "dependencies": {"@ant-design/icons": "^6.0.0", "@icons-pack/react-simple-icons": "^13.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-use-controllable-state": "^1.2.2", "@shikijs/transformers": "^3.4.2", "@types/js-cookie": "^3.0.6", "@vercel/analytics": "^1.4.1", "antd": "^5.25.4", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "gray-matter": "^4.0.3", "js-cookie": "^3.0.5", "lucide-react": "^0.468.0", "next": "15.2.4", "next-intl": "^4.0.2", "next-mdx-remote-client": "2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "shiki": "^3.4.2", "sonner": "^2.0.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-next": "^0.4.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.19", "concurrently": "^9.1.2", "eslint": "^9", "eslint-config-next": "15.0.4", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5"}}