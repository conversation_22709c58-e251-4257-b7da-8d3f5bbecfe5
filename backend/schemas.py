"""Pydantic models for the CodeOCR API."""

import re

from pydantic import BaseModel, Field, field_validator


class CodeOCRRequest(BaseModel):
    """Defines the structure for a request to the CodeOCR API."""

    image_source: str = Field(..., description="Base64 encoded image data or image URL")
    api_key: str = Field(..., description="API key for the vision model provider")
    api_base: str = Field("https://api.openai.com/v1", description="API base URL")
    model_name: str = Field(..., description="Model name to use for OCR")


class CodeOCRResponse(BaseModel):
    """Defines the structured response for the Code OCR API.

    The response contains a single 'code' field, which is a Markdown-formatted
    code block.
    """

    code: str = Field(
        ...,
        description=(
            "The extracted source code, formatted as a Markdown code block. "
            "It MUST start with ``` followed by the identified language, a newline, "
            "the code, and end with ```. If the language is unknown, use 'text'."
        ),
        examples=["```python\nprint('Hello, World!')\n```", "```text\nSome text\n```"],
    )

    @field_validator("code")
    @classmethod
    def validate_code_block_format(cls, v: str) -> str:
        r"""Validate that the 'code' field is a well-formed Markdown code block.

        The format must be: ```language\n...code...\n```
        """
        if not isinstance(v, str):
            raise TypeError("Input must be a string.")

        trimmed_v = v.strip()

        if not trimmed_v.startswith("```") or not trimmed_v.endswith("```"):
            raise ValueError("Code must be enclosed in triple backticks (```).")

        match = re.match(r"^```(\w+)\n(.*)\n```$", trimmed_v, re.DOTALL)

        if not match:
            raise ValueError(
                "Invalid format. Must be ```language\\n...code...\\n```. "
                "Ensure there is a language identifier after the opening "
                "backticks and a newline."
            )

        language = match.group(1)
        content = match.group(2)

        if not language:
            raise ValueError("Language identifier cannot be empty.")

        if not content.strip():
            raise ValueError("Code content inside the block cannot be empty.")

        return v
