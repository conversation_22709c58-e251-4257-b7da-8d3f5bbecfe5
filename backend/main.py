"""CodeOCR API server for converting code screenshots to editable code."""

import logging

import instructor
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from openai import AsyncOpenAI

from prompts import CODE_OCR_PROMPT_TEMPLATE
from schemas import CodeOCRRequest, CodeOCRResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# --- FastAPI Application ---

app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(request: CodeOCRRequest):
    """Extract code from an image using a vision model.

    Returns it in a structured, validated format. This is a non-streaming endpoint.
    """
    try:
        # Patch the OpenAI client with instructor for structured, validated responses
        client = instructor.patch(
            AsyncOpenAI(api_key=request.api_key, base_url=request.api_base)
        )

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": CODE_OCR_PROMPT_TEMPLATE},
                    {
                        "type": "image_url",
                        "image_url": {"url": request.image_source},
                    },
                ],
            }
        ]

        # Call the API with the response_model for validation and automatic retries
        response = await client.chat.completions.create(
            model=request.model_name,
            response_model=CodeOCRResponse,
            messages=messages,
            max_retries=3,  # Automatically retry up to 3 times on validation errors
        )
        return response

    except Exception as e:
        # Log the exception for debugging purposes
        logger.error("An error occurred during CodeOCR processing: %s", e)
        # Raise a standard HTTP 500 error to the client
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process the image. Error: {str(e)}",
        )


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
