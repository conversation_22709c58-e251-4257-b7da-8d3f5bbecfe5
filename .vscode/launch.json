{"version": "0.2.0", "configurations": [{"name": "Debug Next.js App", "type": "node", "request": "launch", "runtimeExecutable": "pnpm", "runtimeArgs": ["dev"], "cwd": "${workspaceFolder}/frontend", "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--inspect"}, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "sourceMaps": true, "smartStep": true, "restart": true, "timeout": 30000}, {"name": "Debug Next.js Server-side", "type": "node", "request": "attach", "port": 9229, "address": "localhost", "localRoot": "${workspaceFolder}/frontend", "remoteRoot": "${workspaceFolder}/frontend", "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "sourceMaps": true, "restart": true}, {"name": "Debug Python Backend", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["main:app", "--host", "127.0.0.1", "--port", "8000", "--reload"], "jinja": true, "justMyCode": true, "cwd": "${workspaceFolder}/backend"}], "compounds": [{"name": "Debug Full Stack (Next.js + Python)", "configurations": ["Debug Next.js App", "Debug Python Backend"], "stopAll": true, "presentation": {"hidden": false, "group": "", "order": 1}}]}